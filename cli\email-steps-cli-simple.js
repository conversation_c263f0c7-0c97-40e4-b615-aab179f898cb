#!/usr/bin/env node

const readline = require('readline');
const fs = require('fs').promises;
const path = require('path');
const moment = require('moment');

// 引入现有的ZeppLifeSteps模块
const zeppLifeSteps = require('../pages/api/ZeppLifeSteps');

class SimpleEmailStepsCLI {
  constructor() {
    this.historyFile = path.join(__dirname, 'history.json');
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // 邮箱格式验证
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // 获取用户输入
  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  // 保存历史记录
  async saveHistory(record) {
    try {
      const history = await this.loadHistory();
      history.unshift({
        ...record,
        timestamp: new Date().toISOString()
      });
      
      // 保留最近20条记录
      const limitedHistory = history.slice(0, 20);
      await fs.writeFile(this.historyFile, JSON.stringify(limitedHistory, null, 2));
    } catch (error) {
      console.error('保存历史记录失败:', error.message);
    }
  }

  // 加载历史记录
  async loadHistory() {
    try {
      const data = await fs.readFile(this.historyFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      return [];
    }
  }

  // 增强错误处理的步数修改功能
  async updateSteps(email, password, steps, targetDate) {
    try {
      console.log('🔐 开始登录验证...');
      console.log(`📧 使用邮箱: ${email}`);
      console.log(`📅 目标日期: ${targetDate}`);
      console.log(`👟 目标步数: ${steps}`);
      
      const { loginToken, userId } = await zeppLifeSteps.login(email, password);
      console.log('✅ 登录成功，用户ID:', userId);

      console.log('🔑 获取应用令牌...');
      const appToken = await zeppLifeSteps.getAppToken(loginToken);
      console.log('✅ 令牌获取成功');

      console.log(`📊 开始修改步数到 ${steps} 步 (日期: ${targetDate})...`);
      const result = await zeppLifeSteps.updateSteps(loginToken, appToken, steps, targetDate);
      
      console.log('🎉 步数修改成功!');
      console.log('📋 修改结果:', JSON.stringify(result, null, 2));
      
      // 保存到历史记录
      await this.saveHistory({
        email: email,
        steps: steps,
        date: targetDate,
        success: true
      });

      return result;
    } catch (error) {
      console.error('❌ 操作失败:', error.message);
      console.error('📋 错误详情:', error.stack);
      
      // 保存失败记录
      await this.saveHistory({
        email: email,
        steps: steps,
        date: targetDate,
        success: false,
        error: error.message
      });
      
      throw error;
    }
  }

  // 简化的交互式运行
  async run() {
    console.log('🚀 Zepp Life 邮箱步数修改工具 (简化版)');
    console.log('─'.repeat(40));
    console.log('⚠️  注意：此版本密码输入为明文显示');
    console.log('─'.repeat(40));

    try {
      // 获取邮箱
      let email;
      do {
        email = await this.prompt('📧 请输入邮箱地址: ');
        if (!this.validateEmail(email)) {
          console.log('❌ 邮箱格式不正确，请重新输入');
        }
      } while (!this.validateEmail(email));

      console.log(`✅ 邮箱格式正确: ${email}`);

      // 获取密码 (明文输入)
      const password = await this.prompt('🔒 请输入密码 (明文显示): ');
      
      if (!password || password.trim().length === 0) {
        console.log('❌ 密码不能为空');
        return;
      }
      
      console.log('✅ 密码已输入');
      console.log('🔍 调试信息: 密码长度为', password.length, '字符');

      // 获取步数
      console.log('📝 准备获取步数输入...');
      const stepsInput = await this.prompt('👟 请输入目标步数 (回车使用随机步数): ');
      const steps = stepsInput.trim() ? parseInt(stepsInput) : Math.floor(Math.random() * 10000) + 20000;

      if (isNaN(steps) || steps < 0) {
        console.log('❌ 步数格式不正确');
        return;
      }

      console.log('📝 准备获取日期输入...');
      // 获取日期
      const dateInput = await this.prompt('📅 请输入日期 (YYYY-MM-DD，回车使用今天): ');
      const targetDate = dateInput.trim() || moment().format('YYYY-MM-DD');

      if (!moment(targetDate, 'YYYY-MM-DD', true).isValid()) {
        console.log('❌ 日期格式不正确');
        return;
      }

      console.log(`\n📋 确认信息:`);
      console.log(`   邮箱: ${email}`);
      console.log(`   步数: ${steps}`);
      console.log(`   日期: ${targetDate}`);

      const confirm = await this.prompt('\n✅ 确认执行? (y/N): ');
      if (confirm.toLowerCase() !== 'y') {
        console.log('❌ 操作已取消');
        return;
      }

      console.log('\n🚀 开始执行操作...\n');
      await this.updateSteps(email, password, steps, targetDate);

    } catch (error) {
      console.error('\n💥 程序执行出错:');
      console.error('错误信息:', error.message);
      console.error('错误堆栈:', error.stack);
      
      // 如果是网络错误，提供更友好的提示
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        console.error('🌐 网络连接失败，请检查网络连接');
      } else if (error.response && error.response.status === 401) {
        console.error('🔐 登录失败，请检查邮箱和密码是否正确');
      }
    } finally {
      this.rl.close();
    }
  }
}

// 主程序入口
async function main() {
  const cli = new SimpleEmailStepsCLI();
  await cli.run();
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = SimpleEmailStepsCLI;
