#!/usr/bin/env node

const readline = require('readline');
const fs = require('fs').promises;
const path = require('path');
const moment = require('moment');

// 引入现有的ZeppLifeSteps模块
const zeppLifeSteps = require('../pages/api/ZeppLifeSteps');

class EmailStepsCLI {
  constructor() {
    this.historyFile = path.join(__dirname, 'history.json');
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // 邮箱格式验证
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // 获取用户输入
  async prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  // 隐藏密码输入 - 简化版本，更好的兼容性
  async promptPassword(question) {
    return new Promise((resolve) => {
      process.stdout.write(question);

      // 检查是否支持 setRawMode
      if (typeof process.stdin.setRawMode === 'function') {
        process.stdin.setRawMode(true);
      }

      process.stdin.resume();
      process.stdin.setEncoding('utf8');

      let password = '';
      let isResolved = false;

      const onData = (char) => {
        if (isResolved) return;

        char = char + '';
        switch(char) {
          case '\n':
          case '\r':
          case '\u0004':
            isResolved = true;
            if (typeof process.stdin.setRawMode === 'function') {
              process.stdin.setRawMode(false);
            }
            process.stdin.pause();
            process.stdin.removeListener('data', onData);
            process.stdout.write('\n');
            resolve(password);
            break;
          case '\u0003':
            process.exit();
          case '\u007f': // 退格键
            if (password.length > 0) {
              password = password.slice(0, -1);
              process.stdout.write('\b \b');
            }
            break;
          default:
            if (char.charCodeAt(0) >= 32) { // 只处理可打印字符
              password += char;
              process.stdout.write('*');
            }
            break;
        }
      };

      process.stdin.on('data', onData);

      // 添加超时处理，防止卡死
      setTimeout(() => {
        if (!isResolved) {
          console.log('\n⚠️  密码输入超时，请重新运行程序');
          process.exit(1);
        }
      }, 60000); // 60秒超时
    });
  }

  // 加载历史记录
  async loadHistory() {
    try {
      const data = await fs.readFile(this.historyFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      return [];
    }
  }

  // 保存历史记录
  async saveHistory(record) {
    try {
      const history = await this.loadHistory();
      history.unshift({
        ...record,
        timestamp: new Date().toISOString()
      });
      
      // 保留最近20条记录
      const limitedHistory = history.slice(0, 20);
      await fs.writeFile(this.historyFile, JSON.stringify(limitedHistory, null, 2));
    } catch (error) {
      console.error('保存历史记录失败:', error.message);
    }
  }

  // 显示历史记录
  async showHistory() {
    try {
      const history = await this.loadHistory();
      if (history.length === 0) {
        console.log('📝 暂无历史记录');
        return;
      }

      console.log('\n📝 最近的修改记录:');
      console.log('─'.repeat(60));
      
      history.slice(0, 10).forEach((record, index) => {
        const date = new Date(record.timestamp).toLocaleString('zh-CN');
        console.log(`${index + 1}. ${record.email} | ${record.steps}步 | ${record.date} | ${date}`);
      });
      console.log('─'.repeat(60));
    } catch (error) {
      console.error('读取历史记录失败:', error.message);
    }
  }

  // 增强错误处理的步数修改功能
  async updateSteps(email, password, steps, targetDate) {
    try {
      console.log('🔐 开始登录验证...');
      console.log(`📧 使用邮箱: ${email}`);
      console.log(`📅 目标日期: ${targetDate}`);
      console.log(`👟 目标步数: ${steps}`);
      
      const { loginToken, userId } = await zeppLifeSteps.login(email, password);
      console.log('✅ 登录成功，用户ID:', userId);

      console.log('🔑 获取应用令牌...');
      const appToken = await zeppLifeSteps.getAppToken(loginToken);
      console.log('✅ 令牌获取成功');

      console.log(`📊 开始修改步数到 ${steps} 步 (日期: ${targetDate})...`);
      const result = await zeppLifeSteps.updateSteps(loginToken, appToken, steps, targetDate);
      
      console.log('🎉 步数修改成功!');
      console.log('📋 修改结果:', JSON.stringify(result, null, 2));
      
      // 保存到历史记录
      await this.saveHistory({
        email: email,
        steps: steps,
        date: targetDate,
        success: true
      });

      return result;
    } catch (error) {
      console.error('❌ 操作失败:', error.message);
      console.error('📋 错误详情:', error.stack);
      
      // 保存失败记录
      await this.saveHistory({
        email: email,
        steps: steps,
        date: targetDate,
        success: false,
        error: error.message
      });
      
      throw error;
    }
  }

  // 修复交互式运行中的错误处理
  async run() {
    console.log('🚀 Zepp Life 邮箱步数修改工具');
    console.log('─'.repeat(40));

    try {
      // 获取邮箱
      let email;
      do {
        email = await this.prompt('📧 请输入邮箱地址: ');
        if (!this.validateEmail(email)) {
          console.log('❌ 邮箱格式不正确，请重新输入');
        }
      } while (!this.validateEmail(email));

      console.log(`✅ 邮箱格式正确: ${email}`);

      // 获取密码
      const password = await this.promptPassword('🔒 请输入密码: ');
      
      if (!password || password.trim().length === 0) {
        console.log('❌ 密码不能为空');
        return;
      }
      
      console.log('✅ 密码已输入');
      console.log('🔍 调试信息: 密码长度为', password.length, '字符');

      // 获取步数
      console.log('📝 准备获取步数输入...');
      const stepsInput = await this.prompt('👟 请输入目标步数 (回车使用随机步数): ');
      const steps = stepsInput.trim() ? parseInt(stepsInput) : Math.floor(Math.random() * 10000) + 20000;

      if (isNaN(steps) || steps < 0) {
        console.log('❌ 步数格式不正确');
        return;
      }

      // 获取日期
      const dateInput = await this.prompt('📅 请输入日期 (YYYY-MM-DD，回车使用今天): ');
      const targetDate = dateInput.trim() || moment().format('YYYY-MM-DD');

      if (!moment(targetDate, 'YYYY-MM-DD', true).isValid()) {
        console.log('❌ 日期格式不正确');
        return;
      }

      console.log(`\n📋 确认信息:`);
      console.log(`   邮箱: ${email}`);
      console.log(`   步数: ${steps}`);
      console.log(`   日期: ${targetDate}`);

      const confirm = await this.prompt('\n✅ 确认执行? (y/N): ');
      if (confirm.toLowerCase() !== 'y') {
        console.log('❌ 操作已取消');
        return;
      }

      console.log('\n🚀 开始执行操作...\n');
      await this.updateSteps(email, password, steps, targetDate);

    } catch (error) {
      console.error('\n💥 程序执行出错:');
      console.error('错误信息:', error.message);
      console.error('错误堆栈:', error.stack);
      
      // 如果是网络错误，提供更友好的提示
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        console.error('🌐 网络连接失败，请检查网络连接');
      } else if (error.response && error.response.status === 401) {
        console.error('🔐 登录失败，请检查邮箱和密码是否正确');
      }
    } finally {
      this.rl.close();
    }
  }

  // 命令行参数模式
  async runWithArgs(args) {
    const email = args.email;
    const password = args.password;
    const steps = args.steps || Math.floor(Math.random() * 10000) + 20000;
    const targetDate = args.date || moment().format('YYYY-MM-DD');

    if (!email || !password) {
      console.log('❌ 邮箱和密码不能为空');
      return;
    }

    if (!this.validateEmail(email)) {
      console.log('❌ 邮箱格式不正确');
      return;
    }

    try {
      await this.updateSteps(email, password, steps, targetDate);
    } finally {
      this.rl.close();
    }
  }
}

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2);
  const parsed = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if (arg === '--email' || arg === '-e') {
      parsed.email = args[++i];
    } else if (arg === '--password' || arg === '-p') {
      parsed.password = args[++i];
    } else if (arg === '--steps' || arg === '-s') {
      parsed.steps = parseInt(args[++i]);
    } else if (arg === '--date' || arg === '-d') {
      parsed.date = args[++i];
    } else if (arg === '--history' || arg === '-h') {
      parsed.showHistory = true;
    } else if (arg === '--help') {
      parsed.help = true;
    }
  }

  return parsed;
}

// 显示帮助信息
function showHelp() {
  console.log(`
🚀 Zepp Life 邮箱步数修改工具

用法:
  node email-steps-cli.js                    # 交互式模式
  node email-steps-cli.js [选项]             # 命令行模式

选项:
  -e, --email <email>      邮箱地址
  -p, --password <pwd>     密码
  -s, --steps <number>     目标步数 (可选，默认随机)
  -d, --date <YYYY-MM-DD>  目标日期 (可选，默认今天)
  -h, --history           显示历史记录
  --help                  显示帮助信息

示例:
  node email-steps-cli.js -e <EMAIL> -p password123 -s 25000
  node email-steps-cli.js -e <EMAIL> -p password123 -d 2024-01-15
  node email-steps-cli.js --history
`);
}

// 主程序入口
async function main() {
  const args = parseArgs();
  const cli = new EmailStepsCLI();

  if (args.help) {
    showHelp();
    return;
  }

  if (args.showHistory) {
    await cli.showHistory();
    return;
  }

  if (args.email && args.password) {
    await cli.runWithArgs(args);
  } else {
    await cli.run();
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = EmailStepsCLI;

