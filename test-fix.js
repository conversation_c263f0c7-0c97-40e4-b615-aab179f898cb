#!/usr/bin/env node

// 快速测试修复后的步数修改功能
const zeppLifeSteps = require('./pages/api/ZeppLifeSteps');

async function testStepsUpdate() {
  console.log('🧪 测试修复后的步数修改功能...\n');
  
  // 测试参数
  const email = '<EMAIL>';
  const password = 'cccc8888';
  const steps = Math.floor(Math.random() * 1000) + 9000; // 9000-9999之间的随机步数
  const targetDate = '2025-07-17';
  
  try {
    console.log('📧 邮箱:', email);
    console.log('👟 目标步数:', steps);
    console.log('📅 目标日期:', targetDate);
    console.log('');
    
    // 登录
    console.log('🔐 开始登录...');
    const { loginToken, userId } = await zeppLifeSteps.login(email, password);
    console.log('✅ 登录成功，用户ID:', userId);
    
    // 获取app token
    console.log('🔑 获取应用令牌...');
    const appToken = await zeppLifeSteps.getAppToken(loginToken);
    console.log('✅ 令牌获取成功');
    
    // 修改步数
    console.log('📊 开始修改步数...');
    const result = await zeppLifeSteps.updateSteps(loginToken, appToken, steps, targetDate);
    
    console.log('\n🎉 测试成功！');
    console.log('📋 修改结果:', JSON.stringify(result, null, 2));
    console.log('\n✅ 步数修改功能已修复，现在应该能正常工作了！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('📋 错误详情:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testStepsUpdate().catch(console.error);
}

module.exports = testStepsUpdate;
